import {
  <PERSON><PERSON>erWindow,
  <PERSON><PERSON><PERSON><PERSON>indowConstructorOptions,
  shell,
  screen,
  app,
} from "electron"
import path from "path"
import { getRendererHandlers } from "@egoist/tipc/main"
import {
  makeKeyWindow,
  makePanel,
  makeWindow,
} from "@egoist/electron-panel-window"
import liquidGlass from "electron-liquid-glass"
import { RendererHandlers } from "./renderer-handlers"
import { configStore } from "./config"
import { getFocusedAppInfo } from "./keyboard"
import { state, agentProcessManager } from "./state"
import { calculatePanelPosition } from "./panel-position"


const isMac = process.platform === "darwin"

type WINDOW_ID = "main" | "panel" | "setup"

export const WINDOWS = new Map<WINDOW_ID, BrowserWindow>()

function createBaseWindow({
  id,
  url,
  showWhenReady = true,
  windowOptions,
}: {
  id: WINDOW_ID
  url?: string
  showWhenReady?: boolean
  windowOptions?: BrowserWindowConstructorOptions
}) {
  // Create the browser window.
  const win = new BrowserWindow({
    width: 900,
    height: 670,
    show: false,
    autoHideMenuBar: true,
    ...windowOptions,
    webPreferences: {
      preload: path.join(__dirname, "../preload/index.mjs"),
      sandbox: false,
      ...windowOptions?.webPreferences,
    },
  })

  WINDOWS.set(id, win)

  if (showWhenReady) {
    win.on("ready-to-show", () => {
      win.show()
    })
  }

  win.on("close", () => {
    WINDOWS.delete(id)
  })

  win.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: "deny" }
  })

  const baseUrl = import.meta.env.PROD
    ? "assets://app"
    : process.env["ELECTRON_RENDERER_URL"]

  const fullUrl = `${baseUrl}${url || ""}`
  win.loadURL(fullUrl)

  return win
}

export function createMainWindow({ url }: { url?: string } = {}) {
  const win = createBaseWindow({
    id: "main",
    url,
    windowOptions: {
      titleBarStyle: "hiddenInset",
      ...(isMac ? { transparent: true } : {}),
    },
  })

  if (isMac) {
    // Required for traffic light buttons when using transparent windows
    try {
      win.setWindowButtonVisibility(true)
    } catch (err) {
      // noop on non-mac or older electron
    }

    // Apply native Liquid Glass effect after the content loads
    win.webContents.once("did-finish-load", () => {
      try {
        liquidGlass.addView(win.getNativeWindowHandle(), {
          cornerRadius: 12,
        })
      } catch (e) {
        console.warn("Failed to apply liquid glass to main window:", e)
      }
    })
  }

  if (process.env.IS_MAC) {
    win.on("close", () => {
      if (configStore.get().hideDockIcon) {
        app.setActivationPolicy("accessory")
        app.dock.hide()
      }
    })

    win.on("show", () => {
      if (configStore.get().hideDockIcon && !app.dock.isVisible()) {
        app.dock.show()
      }
    })
  }

  return win
}

export function createSetupWindow() {
  const win = createBaseWindow({
    id: "setup",
    url: "/setup",
    windowOptions: {
      titleBarStyle: "hiddenInset",
      width: 800,
      height: 600,
      resizable: false,
    },
  })

  return win
}

export function showMainWindow(url?: string) {
  const win = WINDOWS.get("main")

  if (win) {
    win.show()
    if (url) {
      getRendererHandlers<RendererHandlers>(win.webContents).navigate.send(url)
    }
  } else {
    createMainWindow({ url })
  }
}

const panelWindowSize = {
  width: 260,
  height: 50,
}

const agentPanelWindowSize = {
  width: 600,
  height: 400,
}

const textInputPanelWindowSize = {
  width: 380,
  height: 180,
}

const getPanelWindowPosition = (
  mode: "normal" | "agent" | "textInput" = "normal",
) => {
  let size = panelWindowSize
  if (mode === "agent") {
    size = agentPanelWindowSize
  } else if (mode === "textInput") {
    size = textInputPanelWindowSize
  }

  return calculatePanelPosition(size, mode)
}

export function createPanelWindow() {
  const position = getPanelWindowPosition()

  const win = createBaseWindow({
    id: "panel",
    url: "/panel",
    showWhenReady: false,
    windowOptions: {
      hiddenInMissionControl: true,
      skipTaskbar: true,
      closable: false,
      maximizable: false,
      frame: false,
      ...(isMac ? { transparent: true as const } : {}),
      paintWhenInitiallyHidden: true,
      // hasShadow: false,
      width: panelWindowSize.width,
      height: panelWindowSize.height,
      maxWidth: panelWindowSize.width,
      maxHeight: panelWindowSize.height,
      minWidth: panelWindowSize.width,
      minHeight: panelWindowSize.height,
      visualEffectState: "active",
      ...(isMac ? {} : { vibrancy: "under-window" as const }),
      alwaysOnTop: true,
      x: position.x,
      y: position.y,
    },
  })

  if (isMac) {
    // Use native glass on macOS for the panel window
    win.webContents.once("did-finish-load", () => {
      try {
        liquidGlass.addView(win.getNativeWindowHandle(), {
          cornerRadius: 12,
          opaque: false,
        })
      } catch (e) {
        console.warn("Failed to apply liquid glass to panel window:", e)
      }
    })
  }

  win.on("hide", () => {
    getRendererHandlers<RendererHandlers>(win.webContents).stopRecording.send()
  })

  makePanel(win)

  return win
}

export function showPanelWindow() {
  const win = WINDOWS.get("panel")
  if (win) {
    // Determine the correct mode based on current state
    let mode: "normal" | "agent" | "textInput" = "normal"
    if (state.isTextInputActive) {
      mode = "textInput"
    }
    // Note: Agent mode positioning is handled separately in resizePanelForAgentMode

    const position = getPanelWindowPosition(mode)

    win.setPosition(position.x, position.y)
    win.showInactive()
    makeKeyWindow(win)
  }
}

export async function showPanelWindowAndStartRecording() {
  // Capture focus before showing panel
  try {
    const focusedApp = await getFocusedAppInfo()
    state.focusedAppBeforeRecording = focusedApp
  } catch (error) {
    state.focusedAppBeforeRecording = null
  }

  showPanelWindow()
  getWindowRendererHandlers("panel")?.startRecording.send()
}

export async function showPanelWindowAndStartMcpRecording() {
  // Capture focus before showing panel
  try {
    const focusedApp = await getFocusedAppInfo()
    state.focusedAppBeforeRecording = focusedApp
  } catch (error) {
    state.focusedAppBeforeRecording = null
  }

  showPanelWindow()
  getWindowRendererHandlers("panel")?.startMcpRecording.send()
}

export async function showPanelWindowAndShowTextInput() {
  // Capture focus before showing panel
  try {
    const focusedApp = await getFocusedAppInfo()
    state.focusedAppBeforeRecording = focusedApp
  } catch (error) {
    state.focusedAppBeforeRecording = null
  }

  // Set text input state first, then show panel (which will use correct positioning)
  state.isTextInputActive = true
  resizePanelForTextInput()
  showPanelWindow() // This will now use textInput mode positioning
  getWindowRendererHandlers("panel")?.showTextInput.send()
}

export function makePanelWindowClosable() {
  const panel = WINDOWS.get("panel")
  if (panel && !panel.isClosable()) {
    makeWindow(panel)
    panel.setClosable(true)
  }
}

export const getWindowRendererHandlers = (id: WINDOW_ID) => {
  const win = WINDOWS.get(id)
  if (!win) return
  return getRendererHandlers<RendererHandlers>(win.webContents)
}

export const stopRecordingAndHidePanelWindow = () => {
  const win = WINDOWS.get("panel")
  if (win) {
    getRendererHandlers<RendererHandlers>(win.webContents).stopRecording.send()

    if (win.isVisible()) {
      win.hide()
    }
  }
}

export const stopTextInputAndHidePanelWindow = () => {
  const win = WINDOWS.get("panel")
  if (win) {
    state.isTextInputActive = false
    getRendererHandlers<RendererHandlers>(win.webContents).hideTextInput.send()
    resizePanelToNormal()

    if (win.isVisible()) {
      win.hide()
    }
  }
}

export const closeAgentModeAndHidePanelWindow = () => {
  const win = WINDOWS.get("panel")
  if (win) {
    // Update agent state
    state.isAgentModeActive = false
    state.shouldStopAgent = false
    state.agentIterationCount = 0

    // Clear agent progress and resize back to normal
    getRendererHandlers<RendererHandlers>(
      win.webContents,
    ).clearAgentProgress.send()
    resizePanelToNormal()

    // Hide the panel after a small delay to ensure resize completes
    setTimeout(() => {
      if (win.isVisible()) {
        win.hide()
      }
    }, 200)
  }
}

export const emergencyStopAgentMode = async () => {
  console.log("Emergency stop triggered for agent mode")

  // Set stop flag immediately
  state.shouldStopAgent = true

  const win = WINDOWS.get("panel")
  if (win) {
    // Send emergency stop signal to renderer
    getRendererHandlers<RendererHandlers>(
      win.webContents,
    ).emergencyStopAgent?.send()

    // Clear agent progress immediately
    getRendererHandlers<RendererHandlers>(
      win.webContents,
    ).clearAgentProgress.send()
  }

  try {
    const processCountBefore = agentProcessManager.getActiveProcessCount()

    // Kill all agent processes immediately (emergency stop)
    agentProcessManager.emergencyStop()

    const processCountAfter = agentProcessManager.getActiveProcessCount()

    // Update state - but keep shouldStopAgent = true so the agent loop can see it
    state.isAgentModeActive = false
    // DON'T reset shouldStopAgent here - let the agent loop handle it
    state.agentIterationCount = 0

    console.log(
      `Emergency stop completed. Killed ${processCountBefore} processes. Remaining: ${processCountAfter}`,
    )
  } catch (error) {
    console.error("Error during emergency stop:", error)
  }

  // Close panel and resize
  if (win) {
    resizePanelToNormal()
    setTimeout(() => {
      if (win.isVisible()) {
        win.hide()
      }
    }, 100) // Shorter delay for emergency stop
  }
}

export function resizePanelForAgentMode() {
  const win = WINDOWS.get("panel")
  if (!win) {
    return
  }

  const position = getPanelWindowPosition("agent")

  // Update size constraints for agent mode
  win.setMinimumSize(agentPanelWindowSize.width, agentPanelWindowSize.height)
  win.setMaximumSize(agentPanelWindowSize.width, agentPanelWindowSize.height)

  // Set size and position
  win.setSize(agentPanelWindowSize.width, agentPanelWindowSize.height, true) // animate = true
  win.setPosition(position.x, position.y, true) // animate = true
}

export function resizePanelForTextInput() {
  const win = WINDOWS.get("panel")
  if (!win) {
    return
  }

  const position = getPanelWindowPosition("textInput")

  // Update size constraints for text input mode
  win.setMinimumSize(
    textInputPanelWindowSize.width,
    textInputPanelWindowSize.height,
  )
  win.setMaximumSize(
    textInputPanelWindowSize.width,
    textInputPanelWindowSize.height,
  )

  // Set size and position
  win.setSize(
    textInputPanelWindowSize.width,
    textInputPanelWindowSize.height,
    true,
  ) // animate = true
  win.setPosition(position.x, position.y, true) // animate = true
}

export function resizePanelToNormal() {
  const win = WINDOWS.get("panel")
  if (!win) {
    return
  }

  const position = getPanelWindowPosition("normal")

  // Update size constraints back to normal
  win.setMinimumSize(panelWindowSize.width, panelWindowSize.height)
  win.setMaximumSize(panelWindowSize.width, panelWindowSize.height)

  // Set size and position
  win.setSize(panelWindowSize.width, panelWindowSize.height, true) // animate = true
  win.setPosition(position.x, position.y, true) // animate = true
}
